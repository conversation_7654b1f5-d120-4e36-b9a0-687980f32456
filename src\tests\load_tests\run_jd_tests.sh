#!/bin/bash

# JD Load Testing Script
# This script runs various load test scenarios for the JD API

set -e

# Configuration
HOST=${TEST_HOST:-"http://localhost:8000"}
REPORTS_DIR="src/tests/load_tests/reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Create reports directory
mkdir -p $REPORTS_DIR

echo "🚀 Starting JD API Load Tests"
echo "Host: $HOST"
echo "Timestamp: $TIMESTAMP"
echo "Reports will be saved to: $REPORTS_DIR"

# Function to run a test scenario
run_test() {
    local test_name=$1
    local users=$2
    local spawn_rate=$3
    local run_time=$4
    
    echo ""
    echo "📊 Running $test_name test..."
    echo "Users: $users, Spawn Rate: $spawn_rate, Duration: $run_time"
    
    locust -f "src/tests/load_tests/test_jd_load.py" \
           --host="$HOST" \
           --users="$users" \
           --spawn-rate="$spawn_rate" \
           --run-time="$run_time" \
           --headless \
           --html="$REPORTS_DIR/${test_name}_${TIMESTAMP}.html" \
           --csv="$REPORTS_DIR/${test_name}_${TIMESTAMP}" \
           --logfile="$REPORTS_DIR/${test_name}_${TIMESTAMP}.log"
    
    echo "✅ $test_name test completed"
}

# Test Scenarios
echo ""
echo "🧪 Test Scenario 1: Smoke Test (Basic Functionality)"
run_test "smoke_test" 2 1 "1m"

echo ""
echo "🧪 Test Scenario 2: Light Load Test"
run_test "light_load" 5 2 "3m"

echo ""
echo "🧪 Test Scenario 3: Medium Load Test"
run_test "medium_load" 15 5 "5m"

echo ""
echo "🧪 Test Scenario 4: Heavy Load Test"
run_test "heavy_load" 30 10 "10m"

echo ""
echo "🎉 All JD load tests completed!"
echo "📈 Check the reports in: $REPORTS_DIR"
echo ""
echo "📋 Summary of test files generated:"
ls -la "$REPORTS_DIR"/*"$TIMESTAMP"*

echo ""
echo "🔍 To view results:"
echo "1. Open HTML reports in browser: $REPORTS_DIR/*_${TIMESTAMP}.html"
echo "2. Check CSV files for detailed metrics: $REPORTS_DIR/*_${TIMESTAMP}_stats.csv"
echo "3. Review logs for any errors: $REPORTS_DIR/*_${TIMESTAMP}.log"
