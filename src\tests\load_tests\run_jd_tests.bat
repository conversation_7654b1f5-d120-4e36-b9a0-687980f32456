@echo off
REM JD Load Testing Script for Windows
REM This script runs various load test scenarios for the JD API

setlocal enabledelayedexpansion

REM Configuration
set HOST=%TEST_HOST%
if "%HOST%"=="" set HOST=http://localhost:8000

set REPORTS_DIR=src\tests\load_tests\reports
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

REM Create reports directory
if not exist "%REPORTS_DIR%" mkdir "%REPORTS_DIR%"

echo 🚀 Starting JD API Load Tests
echo Host: %HOST%
echo Timestamp: %TIMESTAMP%
echo Reports will be saved to: %REPORTS_DIR%

REM Test Scenarios
echo.
echo 🧪 Test Scenario 1: Smoke Test (Basic Functionality)
locust -f src\tests\load_tests\test_jd_load.py --host=%HOST% --users=2 --spawn-rate=1 --run-time=1m --headless --html=%REPORTS_DIR%\smoke_test_%TIMESTAMP%.html --csv=%REPORTS_DIR%\smoke_test_%TIMESTAMP%
echo ✅ Smoke test completed

echo.
echo 🧪 Test Scenario 2: Light Load Test
locust -f src\tests\load_tests\test_jd_load.py --host=%HOST% --users=5 --spawn-rate=2 --run-time=3m --headless --html=%REPORTS_DIR%\light_load_%TIMESTAMP%.html --csv=%REPORTS_DIR%\light_load_%TIMESTAMP%
echo ✅ Light load test completed

echo.
echo 🧪 Test Scenario 3: Medium Load Test
locust -f src\tests\load_tests\test_jd_load.py --host=%HOST% --users=15 --spawn-rate=5 --run-time=5m --headless --html=%REPORTS_DIR%\medium_load_%TIMESTAMP%.html --csv=%REPORTS_DIR%\medium_load_%TIMESTAMP%
echo ✅ Medium load test completed

echo.
echo 🧪 Test Scenario 4: Heavy Load Test
locust -f src\tests\load_tests\test_jd_load.py --host=%HOST% --users=30 --spawn-rate=10 --run-time=10m --headless --html=%REPORTS_DIR%\heavy_load_%TIMESTAMP%.html --csv=%REPORTS_DIR%\heavy_load_%TIMESTAMP%
echo ✅ Heavy load test completed

echo.
echo 🎉 All JD load tests completed!
echo 📈 Check the reports in: %REPORTS_DIR%
echo.
echo 📋 Summary of test files generated:
dir "%REPORTS_DIR%\*%TIMESTAMP%*"

echo.
echo 🔍 To view results:
echo 1. Open HTML reports in browser: %REPORTS_DIR%\*_%TIMESTAMP%.html
echo 2. Check CSV files for detailed metrics: %REPORTS_DIR%\*_%TIMESTAMP%_stats.csv
echo 3. Review logs for any errors: %REPORTS_DIR%\*_%TIMESTAMP%.log

pause
