"""
Locust load testing for JD Workflow API endpoint.
Tests the GET /v1/jds/{jd_id}/workflows endpoint with various scenarios.
"""

from datetime import datetime
import random
import logging
from locust import HttpUser, task, between

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class JDWorkflowLoadTest(HttpUser):
    """Load test for JD Workflow API with proper authentication and error handling."""

    wait_time = between(1, 3)  # wait between requests (in seconds)

    def on_start(self):
        """Called when user starts - setup headers and test data"""
        self.setup_auth()
        self.setup_test_data()
        logger.info(f"🚀 Workflow test user {id(self)} started")

    def setup_auth(self):
        """Setup authentication headers - supports both auth types"""
        # Since the API supports BOTH auth types, we'll use Bearer token
        self.client.headers.update({
            "Authorization": "Bearer test_token_for_load_testing",
            "Content-Type": "application/json",
            "Accept": "application/json"
        })
        print("✅ Workflow test authentication setup complete")

    def setup_test_data(self):
        """Setup test JD IDs for testing"""
        # Mix of valid and potentially invalid JD IDs for comprehensive testing
        self.valid_jd_ids = [
            "1", "2", "3", "4", "5", "10", "15", "20", "25", "30",
            "100", "101", "102", "200", "250", "300", "500", "1000"
        ]
        
        self.invalid_jd_ids = [
            "0", "-1", "999999", "abc", "null", "undefined", 
            "9999999999", "special@chars", "12.34", ""
        ]

    @task(10)  # High weight - main functionality test
    def get_workflow_valid_jd(self):
        """Test workflow retrieval with valid JD IDs"""
        jd_id = random.choice(self.valid_jd_ids)
        endpoint = f"/v1/jds/{jd_id}/workflows"
        
        print(f"🔍 Testing workflow for JD ID: {jd_id}")
        
        try:
            with self.client.get(
                endpoint,
                catch_response=True,
                name="GET /v1/jds/{jd_id}/workflows - Valid JD"
            ) as response:
                print(f"📡 Request sent for JD {jd_id}, status: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        response_data = response.json()
                        # Check if response has expected structure
                        if self.validate_workflow_response(response_data):
                            response.success()
                            print(f"✅ Workflow retrieved successfully for JD {jd_id}")
                        else:
                            response.failure("Invalid workflow response format")
                            print(f"❌ Invalid response format for JD {jd_id}")
                    except Exception as e:
                        response.failure(f"Response parsing error: {e}")
                        print(f"❌ JSON parsing failed for JD {jd_id}: {e}")
                
                elif response.status_code == 404:
                    # JD not found - this is acceptable for test data
                    response.success()
                    print(f"ℹ️ JD {jd_id} not found (404) - acceptable for test")
                
                elif response.status_code == 401:
                    response.failure("Authentication failed")
                    print(f"❌ Authentication failed for JD {jd_id}")
                
                elif response.status_code == 403:
                    response.failure("Permission denied")
                    print(f"❌ Permission denied for JD {jd_id}")
                
                else:
                    response.failure(f"Unexpected status: {response.status_code}")
                    print(f"❌ Unexpected status {response.status_code} for JD {jd_id}")
                    
        except Exception as e:
            print(f"❌ Exception during workflow request for JD {jd_id}: {e}")

    @task(3)  # Medium weight - test error handling
    def get_workflow_invalid_jd(self):
        """Test workflow retrieval with invalid JD IDs"""
        jd_id = random.choice(self.invalid_jd_ids)
        endpoint = f"/v1/jds/{jd_id}/workflows"
        
        print(f"🧪 Testing workflow with invalid JD ID: {jd_id}")
        
        try:
            with self.client.get(
                endpoint,
                catch_response=True,
                name="GET /v1/jds/{jd_id}/workflows - Invalid JD"
            ) as response:
                print(f"📡 Invalid ID request sent for JD {jd_id}, status: {response.status_code}")
                
                if response.status_code in [400, 404, 422]:
                    # Expected error responses for invalid IDs
                    response.success()
                    print(f"✅ Invalid JD {jd_id} correctly rejected with {response.status_code}")
                
                elif response.status_code == 500:
                    # Server error - should be handled gracefully
                    response.failure("Server error on invalid input")
                    print(f"❌ Server error (500) for invalid JD {jd_id} - should handle gracefully")
                
                elif response.status_code == 401:
                    response.failure("Authentication failed")
                    print(f"❌ Authentication failed for invalid JD {jd_id}")
                
                else:
                    # Any other response is acceptable for invalid input
                    response.success()
                    print(f"ℹ️ Invalid JD {jd_id} returned status {response.status_code}")
                    
        except Exception as e:
            print(f"❌ Exception during invalid workflow request for JD {jd_id}: {e}")

    @task(5)  # Test with sequential JD IDs
    def get_workflow_sequential_jds(self):
        """Test workflow retrieval with sequential JD IDs (simulates browsing)"""
        # Simulate user browsing through JDs sequentially
        base_id = random.randint(1, 100)
        jd_id = str(base_id)
        endpoint = f"/v1/jds/{jd_id}/workflows"
        
        print(f"📋 Sequential browsing test for JD ID: {jd_id}")
        
        try:
            with self.client.get(
                endpoint,
                catch_response=True,
                name="GET /v1/jds/{jd_id}/workflows - Sequential Browse"
            ) as response:
                print(f"📡 Sequential request for JD {jd_id}, status: {response.status_code}")
                
                # For sequential browsing, we expect either success or not found
                if response.status_code in [200, 404]:
                    response.success()
                    if response.status_code == 200:
                        print(f"✅ Sequential workflow found for JD {jd_id}")
                    else:
                        print(f"ℹ️ Sequential JD {jd_id} not found (normal)")
                else:
                    response.failure(f"Unexpected status during sequential browse: {response.status_code}")
                    print(f"❌ Unexpected status {response.status_code} during sequential browse for JD {jd_id}")
                    
        except Exception as e:
            print(f"❌ Exception during sequential workflow request for JD {jd_id}: {e}")

    @task(1)  # Low weight - edge case testing
    def get_workflow_edge_cases(self):
        """Test workflow retrieval with edge case JD IDs"""
        edge_cases = [
            "999999999",  # Very large number
            "1" + "0" * 10,  # Extremely large number
            "%20",  # URL encoded space
            "1/2",  # Contains slash
            "1?test=1",  # Contains query params
            "1#fragment",  # Contains fragment
        ]
        
        jd_id = random.choice(edge_cases)
        # URL encode the JD ID for safety
        import urllib.parse
        encoded_jd_id = urllib.parse.quote(str(jd_id), safe='')
        endpoint = f"/v1/jds/{encoded_jd_id}/workflows"
        
        print(f"🎯 Edge case test for JD ID: {jd_id} (encoded: {encoded_jd_id})")
        
        try:
            with self.client.get(
                endpoint,
                catch_response=True,
                name="GET /v1/jds/{jd_id}/workflows - Edge Cases"
            ) as response:
                print(f"📡 Edge case request for JD {jd_id}, status: {response.status_code}")
                
                # For edge cases, we mainly want to ensure no server crashes
                if response.status_code in [200, 400, 404, 422]:
                    response.success()
                    print(f"✅ Edge case JD {jd_id} handled properly with {response.status_code}")
                
                elif response.status_code == 500:
                    response.failure("Server error on edge case - should handle gracefully")
                    print(f"❌ Server error (500) for edge case JD {jd_id}")
                
                else:
                    response.success()  # Other responses are acceptable for edge cases
                    print(f"ℹ️ Edge case JD {jd_id} returned status {response.status_code}")
                    
        except Exception as e:
            print(f"❌ Exception during edge case workflow request for JD {jd_id}: {e}")

    @task(2)  # Health check for the workflow endpoint
    def workflow_health_check(self):
        """Simple health check using a known pattern"""
        jd_id = "1"  # Use a simple, likely valid ID
        endpoint = f"/v1/jds/{jd_id}/workflows"
        
        print(f"🔄 Workflow health check with JD ID: {jd_id}")
        
        try:
            with self.client.get(
                endpoint,
                catch_response=True,
                name="GET /v1/jds/{jd_id}/workflows - Health Check"
            ) as response:
                print(f"📡 Health check response: {response.status_code}")
                
                # Health check passes if we get any valid HTTP response
                if response.status_code in [200, 401, 403, 404]:
                    response.success()
                    print(f"✅ Workflow endpoint health check passed: {response.status_code}")
                else:
                    response.failure(f"Health check failed: {response.status_code}")
                    print(f"❌ Workflow endpoint health check failed: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ Exception during workflow health check: {e}")

    def validate_workflow_response(self, response_data):
        """Validate the structure of workflow response"""
        try:
            # Check if response has expected workflow structure
            # Adjust these checks based on your actual WorkflowModel structure
            if isinstance(response_data, dict):
                # Look for common workflow fields (adjust as needed)
                expected_fields = ['success', 'data', 'message']  # Adjust based on your response format
                return any(field in response_data for field in expected_fields)
            return False
        except Exception:
            return False


# Specialized user types for different usage patterns
class WorkflowBrowser(JDWorkflowLoadTest):
    """Simulates users browsing workflows sequentially"""
    wait_time = between(2, 4)  # Slower, more deliberate browsing
    weight = 3

    def on_start(self):
        super().on_start()
        print(f"👀 Workflow browser {id(self)} started - browsing mode")

    # Override task weights for browsing behavior
    @task(15)
    def browse_workflows(self):
        self.get_workflow_sequential_jds()

    @task(5)
    def check_specific_workflow(self):
        self.get_workflow_valid_jd()


class WorkflowValidator(JDWorkflowLoadTest):
    """Simulates automated systems or validators testing the API"""
    wait_time = between(0.5, 1.5)  # Faster, automated testing
    weight = 1

    def on_start(self):
        super().on_start()
        print(f"🤖 Workflow validator {id(self)} started - validation mode")

    # Override task weights for validation behavior
    @task(10)
    def validate_workflows(self):
        self.get_workflow_valid_jd()

    @task(8)
    def test_error_handling(self):
        self.get_workflow_invalid_jd()

    @task(5)
    def test_edge_cases(self):
        self.get_workflow_edge_cases()


class NormalWorkflowUser(JDWorkflowLoadTest):
    """Simulates normal users checking workflows occasionally"""
    wait_time = between(3, 8)  # Normal user pace
    weight = 5

    def on_start(self):
        super().on_start()
        print(f"👤 Normal workflow user {id(self)} started - casual usage")