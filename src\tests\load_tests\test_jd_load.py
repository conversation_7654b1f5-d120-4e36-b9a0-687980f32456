from locust import HttpUser, task, between
import json

from src.tests.load_tests.test_main import override_current_user

class JDLoadTest(HttpUser):
    wait_time = between(1, 3)  # wait between requests (in seconds)
    test_user = override_current_user()


    @task
    def create_jd(self):
        jd_payload = {
  "jd_name": "Senior Backend Engineer - Python",
  "jd_status": "DRAFT",
  "assignee_ids": [12345, 67890],
  "jd_text": "We are looking for a skilled Senior Backend Engineer with experience in Python, Django/FastAPI, and PostgreSQL to build scalable APIs and backend systems.",
  "hiring_priority": "HIGH",
  "designation": "Senior Backend Engineer",
  "min_work_exp": 5,
  "max_work_exp": 8,
  "job_type": "FULL_TIME",
  "job_preference": "REMOTE",
  "primary_skills": "Python, Django, FastAPI, PostgreSQL",
  "secondary_skills": "<PERSON><PERSON>, <PERSON><PERSON>, AWS, Git",
  "preferred_location": "Bangalore, India",
  "no_of_positions": 2,
  "company_name": "TechNova Solutions",
  "client_name": "Acme Corp",
  "company_url": "https://www.technova.com",
  "department": "IT",
  "notice_period": "Immediate to 30 days",
  "salary_currency": "INR",
  "min_salary": 1500000,
  "max_salary": 2200000,
  "salary_duration": "YEARLY",
  "confirmed_start_date": "2025-08-01T09:00:00.000Z",
  "bill_to_client": "Yes",
  "contract_acceptance": True,
  "recommendation_skills": "REST APIs, Microservices, PostgreSQL tuning",
  "recommendation_keywords": "Senior Python Engineer, Django developer, backend systems",
  "recommendation_companies": "Freshworks, Zoho, ThoughtWorks",
  "recommendation_industries": "SaaS, FinTech, EdTech",
  "recommendation_colleges": "IITs, NITs, BITS Pilani",
  "targets_shortlisted": 5,
  "targets_shortlisted_date": "2025-08-10T10:00:00.000Z",
  "targets_hiring_date": "2025-09-01T10:00:00.000Z",
  "tax_term": "W2",
  "vendor_email": "<EMAIL>",
  "vendor_phone": "+91-9876543210",
  "vendor_link": "https://www.technova.com/vendor-portal"
}


        self.client.post("/v1/jds", headers=self.headers, data=json.dumps(jd_payload))
