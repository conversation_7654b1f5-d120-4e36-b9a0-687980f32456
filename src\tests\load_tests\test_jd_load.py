"""
Locust load testing for JD (Job Description) API endpoints.
This file contains corrected and improved load tests.
"""

from datetime import datetime

from locust import HttpUser, task, between


class JDLoadTest(HttpUser):
    """Load test for JD creation API with proper authentication and error handling."""

    wait_time = between(1, 3)  # wait between requests (in seconds)

    def on_start(self):
        """Called when user starts - setup headers"""
        self.setup_auth()
        self.jd_counter = 0

    def setup_auth(self):
        """Setup authentication headers - using test token since app has test user override"""
        # Since the app has a test user override in get_current_user,
        # we just need to provide any Bearer token
        self.client.headers.update({
            "Authorization": "Bearer test_token_for_load_testing",
            "Content-Type": "application/json"
        })
        print("✅ Test authentication setup complete")

    def generate_jd_payload(self):
        """Generate dynamic JD data for testing"""
        self.jd_counter += 1

        payload={
                "jd_name": "Senior Backend Engineer - Python",
                "jd_status": "DRAFT",
                "assignee_ids": ["12345", "67890"],
                "jd_text": "We are looking for a skilled Senior Backend Engineer with experience in Python, Django/FastAPI, and PostgreSQL to build scalable APIs and backend systems.",
                "hiring_priority": "HIGH",
                "designation": "Senior Backend Engineer",
                "min_work_exp": 5,
                "max_work_exp": 8,
                "job_type": "FULL_TIME",
                "job_preference": "REMOTE",
                "primary_skills": "Python, Django, FastAPI, PostgreSQL",
                "secondary_skills": "Docker, Redis, AWS, Git",
                "preferred_location": "Bangalore, India",
                "no_of_positions": 2,
                "company_name": "TechNova Solutions",
                "client_name": "Acme Corp",
                "company_url": "https://www.technova.com",
                "department": "HR",
                "notice_period": "Immediate to 30 days",
                "salary_currency": "INR",
                "min_salary": 1500000,
                "max_salary": 2200000,
                "salary_duration": "YEARLY",
                "confirmed_start_date": "2025-08-01T09:00:00.000Z",
                "bill_to_client": "Yes",
                "contract_acceptance": True,
                "recommendation_skills": "REST APIs, Microservices, PostgreSQL tuning",
                "recommendation_keywords": "Senior Python Engineer, Django developer, backend systems",
                "recommendation_companies": "Freshworks, Zoho, ThoughtWorks",
                "recommendation_industries": "SaaS, FinTech, EdTech",
                "recommendation_colleges": "IITs, NITs, BITS Pilani",
                "targets_shortlisted": 5,
                "targets_shortlisted_date": "2025-08-10T10:00:00.000Z",
                "targets_hiring_date": "2025-09-01T10:00:00.000Z",
                "tax_term": "W2",
                "vendor_email": "<EMAIL>",
                "vendor_phone": "+91-9876543210",
                "vendor_link": "https://www.technova.com/vendor-portal"
                }


    @task(3)
    def create_jd_success_case(self):
        """Test successful JD creation"""
        jd_payload = self.generate_jd_payload()

        with self.client.post(
            "/v1/jds",
            json=jd_payload,  # Use json parameter, not data
            catch_response=True,
            name="POST /v1/jds - Create JD"
        ) as response:
            if response.status_code in [200, 201]:
                try:
                    response_data = response.json()
                    if "jd_id" in response_data and "jd_name" in response_data:
                        response.success()
                        print(f"✅ JD created successfully: {jd_payload['jd_name']}")
                    else:
                        response.failure("Invalid response format - missing required fields")
                except Exception as e:
                    response.failure(f"Response parsing error: {str(e)}")
            elif response.status_code == 401:
                response.failure("Authentication failed - check test setup")
            elif response.status_code == 403:
                response.failure("Permission denied - check user permissions")
            elif response.status_code == 422:
                response.failure(f"Validation error: {response.text}")
            else:
                response.failure(f"HTTP {response.status_code}: {response.text}")

    @task(1)
    def create_jd_minimal_data(self):
        """Test JD creation with minimal required fields"""
        self.jd_counter += 1
        minimal_payload = {
            "jd_name": f"Minimal Test JD {self.jd_counter}",
            "designation": "Test Position",
            "assignee_ids": ["1"],  # Fixed: API expects strings
            "jd_text": "Minimal test job description",
            "hiring_priority": "MEDIUM",
            "min_work_exp": 1,
            "job_type": "FULL_TIME",
            "job_preference": "ON_SITE",
            "primary_skills": "Basic Skills",
            "preferred_location": "Test Location",
            "no_of_positions": 1,
            "company_name": "Test Company",
            "notice_period": "30 days"
        }

        with self.client.post(
            "/v1/jds",
            json=minimal_payload,
            catch_response=True,
            name="POST /v1/jds - Minimal Data"
        ) as response:
            if response.status_code in [200, 201]:
                response.success()
                print(f"✅ Minimal JD created: {minimal_payload['jd_name']}")
            else:
                error_msg = f"Minimal data test failed: {response.status_code} - {response.text}"
                response.failure(error_msg)

    @task(1)
    def create_jd_invalid_data(self):
        """Test JD creation with invalid data (should fail gracefully)"""
        invalid_payload = {
            "jd_name": "",  # Empty name should fail
            "designation": None,  # Null designation should fail
            "assignee_ids": [],  # Empty assignees should fail
            "jd_text": None,  # Null text should fail
            "hiring_priority": "INVALID_PRIORITY"  # Invalid priority should fail
        }

        with self.client.post(
            "/v1/jds",
            json=invalid_payload,
            catch_response=True,
            name="POST /v1/jds - Invalid Data"
        ) as response:
            if response.status_code in [400, 422]:  # Expected validation errors
                response.success()
                print("✅ Invalid data correctly rejected")
            else:
                response.failure(f"Expected validation error, got: {response.status_code}")

# Additional user types for realistic testing
class HeavyJDCreator(JDLoadTest):
    """Simulates users who create JDs rapidly"""
    wait_time = between(0.5, 1)  # Faster creation
    weight = 1  # Lower weight (fewer users of this type)

class NormalJDCreator(JDLoadTest):
    """Simulates normal HR users"""
    wait_time = between(2, 5)  # Normal pace
    weight = 3  # Higher weight (more users of this type)
