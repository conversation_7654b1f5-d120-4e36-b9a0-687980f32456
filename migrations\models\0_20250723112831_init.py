from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "t_jd" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "modified_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "status" BOOL NOT NULL  DEFAULT True,
    "jd_id" BIGINT NOT NULL  PRIMARY KEY,
    "hid" VARCHAR(255) NOT NULL UNIQUE,
    "jd_name" VARCHAR(255) NOT NULL,
    "jd_text" TEXT NOT NULL,
    "company_id" INT NOT NULL,
    "hiring_priority" SMALLINT NOT NULL,
    "designation" VARCHAR(255) NOT NULL,
    "min_work_exp" INT NOT NULL,
    "max_work_exp" INT,
    "job_type" SMALLINT NOT NULL,
    "job_preference" SMALLINT NOT NULL,
    "primary_skills" TEXT NOT NULL,
    "secondary_skills" TEXT,
    "preferred_location" TEXT NOT NULL,
    "no_of_positions" SMALLINT NOT NULL,
    "company_name" VARCHAR(255) NOT NULL,
    "company_url" VARCHAR(1000),
    "department" SMALLINT,
    "notice_period_in_days" SMALLINT,
    "notice_period" VARCHAR(255) NOT NULL,
    "salary_currency" VARCHAR(3),
    "min_salary" DOUBLE PRECISION,
    "max_salary" DOUBLE PRECISION,
    "salary_duration" SMALLINT,
    "hourly_rate" VARCHAR(100),
    "confirmed_start_date" DATE,
    "bill_to_client" VARCHAR(100),
    "contract_acceptance" BOOL,
    "tax_term" VARCHAR(100),
    "recommendation_skills" VARCHAR(1000),
    "recommendation_keywords" VARCHAR(1000),
    "recommendation_companies" VARCHAR(1000),
    "recommendation_industries" VARCHAR(1000),
    "recommendation_colleges" VARCHAR(1000),
    "targets_shortlisted" SMALLINT,
    "targets_shortlisted_date" DATE,
    "targets_hiring_date" DATE,
    "vendor_email" VARCHAR(255),
    "vendor_phone" VARCHAR(255),
    "vendor_link" VARCHAR(1000),
    "application_form" JSONB,
    "career_page" JSONB,
    "created_by" INT NOT NULL,
    "jd_embedding" JSONB NOT NULL,
    "client_name" VARCHAR(255),
    "jd_status" SMALLINT NOT NULL
);
CREATE INDEX IF NOT EXISTS "idx_t_jd_created_cc873c" ON "t_jd" ("created_by");
CREATE  INDEX "idx_t_jd_jd_id_d09f92" ON "t_jd" ("jd_id", "company_id");
COMMENT ON COLUMN "t_jd"."jd_text" IS 'The full text(description) content of the JD';
COMMENT ON COLUMN "t_jd"."company_id" IS 'The ID of the company posting the JD.';
COMMENT ON COLUMN "t_jd"."hiring_priority" IS 'LOW: 1\nMEDIUM: 2\nHIGH: 3';
COMMENT ON COLUMN "t_jd"."job_type" IS 'Job type';
COMMENT ON COLUMN "t_jd"."job_preference" IS 'Job presence';
COMMENT ON COLUMN "t_jd"."department" IS 'HR: 1\nFINANCE: 2\nIT: 3\nMARKETING: 4\nSALES: 5\nOTHER: 6';
COMMENT ON COLUMN "t_jd"."salary_duration" IS 'Salary type';
COMMENT ON COLUMN "t_jd"."hourly_rate" IS 'Hourly rate for contract type';
COMMENT ON COLUMN "t_jd"."confirmed_start_date" IS 'Confirmed start date for contract type';
COMMENT ON COLUMN "t_jd"."bill_to_client" IS 'Bill to client for contract type';
COMMENT ON COLUMN "t_jd"."contract_acceptance" IS 'Contract acceptance for contract type';
COMMENT ON COLUMN "t_jd"."tax_term" IS 'Tax term for contract type';
COMMENT ON COLUMN "t_jd"."created_by" IS 'User ID as FK';
COMMENT ON COLUMN "t_jd"."jd_status" IS 'JD Status';
CREATE TABLE IF NOT EXISTS "t_jd_assign" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "modified_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "status" BOOL NOT NULL  DEFAULT True,
    "id" SERIAL NOT NULL PRIMARY KEY,
    "company_id" INT NOT NULL,
    "assignee_id" INT NOT NULL,
    "assignor_id" INT NOT NULL,
    "comment" TEXT,
    "is_unassigned" BOOL NOT NULL  DEFAULT False,
    "is_withdrawn" BOOL NOT NULL  DEFAULT False,
    "jd_id" BIGINT NOT NULL REFERENCES "t_jd" ("jd_id") ON DELETE CASCADE,
    CONSTRAINT "uid_t_jd_assign_jd_id_98010b" UNIQUE ("jd_id", "assignee_id")
);
CREATE INDEX IF NOT EXISTS "idx_t_jd_assign_assigne_af425f" ON "t_jd_assign" ("assignee_id");
CREATE INDEX IF NOT EXISTS "idx_t_jd_assign_assigno_084c1d" ON "t_jd_assign" ("assignor_id");
COMMENT ON COLUMN "t_jd_assign"."assignee_id" IS 'User ID as FK';
COMMENT ON COLUMN "t_jd_assign"."assignor_id" IS 'User ID as FK';
COMMENT ON TABLE "t_jd_assign" IS 'Represents the assignment of a job description to a user.';
CREATE TABLE IF NOT EXISTS "t_scenario" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "modified_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "status" BOOL NOT NULL  DEFAULT True,
    "id" SERIAL NOT NULL PRIMARY KEY,
    "company_id" INT,
    "is_generated" BOOL NOT NULL  DEFAULT False,
    "category" VARCHAR(255) NOT NULL,
    "description" TEXT NOT NULL,
    "created_by" INT
);
CREATE TABLE IF NOT EXISTS "t_automation" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "modified_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "status" BOOL NOT NULL  DEFAULT True,
    "id" SERIAL NOT NULL PRIMARY KEY,
    "company_id" INT,
    "description" VARCHAR(255),
    "template_id" INT NOT NULL,
    "channel_type" SMALLINT NOT NULL,
    "email_type" SMALLINT,
    "schedule_type" SMALLINT NOT NULL,
    "schedule_time" TIMETZ,
    "created_by" INT,
    "scenario_id" INT NOT NULL REFERENCES "t_scenario" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "t_automation"."channel_type" IS 'channel_type';
COMMENT ON COLUMN "t_automation"."email_type" IS 'email_type';
COMMENT ON COLUMN "t_automation"."schedule_type" IS 'schedule_type';
CREATE TABLE IF NOT EXISTS "t_workflow" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "modified_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "status" BOOL NOT NULL  DEFAULT True,
    "id" SERIAL NOT NULL PRIMARY KEY,
    "company_id" INT,
    "is_generated" BOOL NOT NULL  DEFAULT False,
    "name" VARCHAR(255) NOT NULL UNIQUE,
    "workflow" JSONB NOT NULL,
    "created_by" INT
);
CREATE TABLE IF NOT EXISTS "t_workflow_jd" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "modified_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "status" BOOL NOT NULL  DEFAULT True,
    "id" SERIAL NOT NULL PRIMARY KEY,
    "local_workflow" JSONB,
    "last_updated_by" INT NOT NULL,
    "jd_id" BIGINT NOT NULL REFERENCES "t_jd" ("jd_id") ON DELETE CASCADE,
    "workflow_id" INT NOT NULL REFERENCES "t_workflow" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_t_workflow__jd_id_e305e4" UNIQUE ("jd_id", "workflow_id")
);
CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
