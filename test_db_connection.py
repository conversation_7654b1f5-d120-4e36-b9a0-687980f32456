import asyncpg
import asyncio

async def test_db_connection():
    try:
        conn = await asyncpg.connect('postgresql://postgres:123456@localhost:5432/hire10xdb')
        print('✅ Database connection successful')
        await conn.close()
        return True
    except Exception as e:
        print(f'❌ Database connection failed: {e}')
        return False

if __name__ == "__main__":
    asyncio.run(test_db_connection())
